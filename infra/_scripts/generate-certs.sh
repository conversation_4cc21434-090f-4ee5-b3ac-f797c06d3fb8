#!/bin/bash

# Script para generar certificados SSL locales para msarknet.me y subdominios
# Incluye los nuevos dominios para Cerebro stack

# Crear directorio para certificados
mkdir -p certs

# Crear archivo de configuración OpenSSL para certificado wildcard
cat > certs/msarknet.conf << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = ES
ST = Madrid
L = Madrid
O = MSarkNet Local Development
OU = Development Team
CN = *.msarknet.me

[v3_req]
keyUsage = digitalSignature, keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = msarknet.me
DNS.3 = *.msarknet.me
DNS.4 = whoami.msarknet.me
DNS.5 = traefik.msarknet.me
DNS.6 = grafana.msarknet.me
DNS.7 = prom.msarknet.me
DNS.8 = portainer.msarknet.me
DNS.9 = docs.msarknet.me
DNS.10 = app.msarknet.me
DNS.11 = api.msarknet.me
DNS.12 = db.msarknet.me
IP.1 = 127.0.0.1
EOF

# Generar clave privada
openssl genrsa -out certs/msarknet.me.key 2048

# Generar certificado autofirmado
openssl req -new -x509 -key certs/msarknet.me.key \
    -out certs/msarknet.me.crt \
    -days 365 \
    -config certs/msarknet.conf \
    -extensions v3_req

# Establecer permisos correctos
chmod 600 certs/msarknet.me.key
chmod 644 certs/msarknet.me.crt

echo "✅ Certificados generados exitosamente:"
echo "   - Certificado: certs/msarknet.me.crt"
echo "   - Clave privada: certs/msarknet.me.key"
echo ""
echo "🔧 Para usar estos certificados:"
echo "   1. Ejecuta: docker compose up -d"
echo "   2. Accede a los servicios disponibles"
echo "   3. Acepta el certificado autofirmado en tu navegador"
echo ""
echo "🚀 Servicios Cerebro disponibles:"
echo "   - https://app.msarknet.me (React Frontend)"
echo "   - https://api.msarknet.me (Node.js Backend)"
echo "   - https://db.msarknet.me (MongoDB - Protegido con auth)"
echo ""
echo "🔧 Servicios originales disponibles:"
echo "   - https://msarknet.me (Aplicación principal)"
echo "   - https://grafana.msarknet.me (Grafana mock)"
echo "   - https://prom.msarknet.me (Prometheus mock)"
echo "   - https://traefik.msarknet.me (Dashboard de Traefik)"
echo "   - https://portainer.msarknet.me (Portainer mock)"
echo "   - https://docs.msarknet.me (Documentación)"
echo "   - https://msarknet.me/api (API endpoint original)"
